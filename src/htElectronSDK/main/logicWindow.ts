/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
import { BrowserWindow, screen } from 'electron';
import { mainIPC, childIPC } from './ipc';

declare const LINKFLOW_PRELOAD_WEBPACK_ENTRY: string;

const getWindowHref = () => {
  if (process.env.SERVER === 'development') {
    return 'http://localhost:8000/linkflow/browserWin';
  } else if (process.env.SERVER === 'sit') {
    return 'http://eipdev.htsc.com.cn/linkflow/browserWin';
  } else if (process.env.SERVER === 'uat') {
    return 'http://eipsit.htsc.com.cn/linkflow/browserWin';
  } else if (process.env.SERVER === 'prod') {
    return 'http://eip.htsc.com.cn/linkflow/browserWin';
  } else if (process.env.SERVER === 'lite') {
    return 'http://eiplite.htsc.com.cn/linkflow/browserWin';
  } else {
    return 'http://eipsit.htsc.com.cn/linkflow/browserWin';
  }
};

const winURL = getWindowHref();
const windowQueue = [];

// 子窗口预创建
const prepareWindow = async () => {
  const winWrapper = await _innerCreate();
  windowQueue.push(winWrapper);
};

// 新增子窗口
const _innerCreate = () => {
  return new Promise((resolve) => {
    const winWrapper: any = {};
    winWrapper.win = new BrowserWindow({
      width: 0,
      height: 0,
      fullscreen: false,
      show: false,
      frame: false,
      center: true,
      backgroundColor: '#fafafa',
      enableLargerThanScreen: true, // 让窗口能布局在扩展屏上
      webPreferences: {
        nodeIntegration: true,
        preload: LINKFLOW_PRELOAD_WEBPACK_ENTRY, // Path.join(__dirname, '../renderer/lighten/preload.js'),
      },
    });
    winWrapper.win.loadURL(`${winURL}`).then(() => {
      resolve(winWrapper);
    });
    winWrapper.win.closeDevTools();
    winWrapper.processId = winWrapper.win.id;
    winWrapper.win.hide();
  });
};

// 公共方法-从预创建的窗口列表里使用一个新窗口
const createWindow = async (props) => {
  const {
    type,
    title,
    option,
    bgColor = null,
    isShow = true,
    params = {},
  } = props;

  let winobj = windowQueue.pop();
  if (!winobj || winobj.win.isDestroyed()) {
    winobj = await _innerCreate();
  }
  // set title、size、frame、topbar
  getOption(option, winobj.win);

  const win = BrowserWindow.fromId(winobj.processId);
  win.webContents.send('update-browserWin', {
    type,
    title,
    params,
  });
  win.restore();
  win.focus();
  setTimeout(async () => {
    // 异步再准备一个窗口
    prepareWindow();
  }, 1);
  isShow &&
    setTimeout(() => {
      bgColor && winobj.win.setBackgroundColor(bgColor);
      winobj.win.show();
    }, 80);
  return winobj.win;
};

// 公共方法-子窗口属性设置
const getOption = (option: any = {}, win) => {
  option.title && win.setTitle(option.title);
  option.resizable && win.setResizable(option.resizable);
  option.alwaysOnTop && win.setAlwaysOnTop(true);
  win.setSkipTaskbar(option.skipTaskbar || false);
  if (option.minWidth && option.minHeight) {
    win.setMinimumSize(option.minWidth, option.minHeight);
  }
  if (option.width && option.height) {
    win.setSize(option.width, option.height);
  }
  const { width, height, x, y } = option;
  win.setBounds({ width, height, x, y });
};

// 公共方法-展示子窗口
const showBrowserWin = (props: {
  type: number;
  title: string;
  option?: any;
  params?: any;
  callback?: (win: BrowserWindow) => void;
}) => {
  const { type: winType, title, option, params, callback } = props;
  const { isSingle = true } = params;
  if (isSingle) {
    const win = childIPC.getChildWindowMap().get(winType);
    if (win && !win.isDestroyed(win)) {
      win.restore();
      win.focus();
      getOption(option, win);
      callback?.(win);
      return win.webContents.id;
    }
  }
  if (childIPC.getPendingMap().has(winType)) {
    // 该类型窗口正在创建中
    // 返回空对象，避免访问直接属性
    return {};
  }
  childIPC.getPendingMap().set(winType, true);
  try {
    return createWindow({
      type: winType,
      title,
      option,
      params,
    }).then((child) => {
      child.on('closed', () => {
        childIPC.updateChildWindow('delete', winType);
      });
      // child.webContents.openDevTools(); // sit包都打开调试器---批注窗口不打开，如需要打开，设置isShowDevtool为false
      // 缺少关闭时的delete事件
      childIPC.updateChildWindow('set', winType, child);
      return child.webContents.id;
    });
  } catch (err) {
  } finally {
    childIPC.getPendingMap().delete(winType);
  }
};

// 固定窗口宽度或者高度的窗口适配
const getOtherWindowSize = (real_width, real_height) => {
  let width = real_width;
  let height = real_height;
  const other_height = 44 + 44; // 扣除顶部底部bar
  // 上下左右的留白
  const width_spare = 120;
  const height_spare = 200 + other_height;
  // 主窗口位置
  const homeWinBounds = mainIPC.getMainWindow().getBounds();

  // 多屏幕时判断是在哪面屏幕打开
  const cursor_point = screen.getCursorScreenPoint();
  const curr_screen = screen.getDisplayNearestPoint({
    x: cursor_point.x,
    y: cursor_point.y,
  });
  const screen_width = curr_screen.bounds.width;
  const screen_height = curr_screen.bounds.height;
  if (width > screen_width - width_spare) {
    width = screen_width - width_spare;
  }
  if (height > screen_height - height_spare) {
    height = screen_height - height_spare;
  }
  width = width > 300 ? width : 300;
  height = height > 400 ? height : 400;
  width = parseInt(width, 10);
  height = parseInt(height + other_height, 10);
  let x = Math.ceil((screen_width - width) / 2);
  let y = Math.ceil((screen_height - height) / 2);
  if (homeWinBounds?.width && homeWinBounds.height) {
    x = Math.ceil(homeWinBounds.x + (homeWinBounds.width - width) / 2);
    y = Math.ceil(homeWinBounds.y + (homeWinBounds.height - height) / 2);
  }
  return [width, height, x, y];
};

// 图片预览窗口-获取窗口尺寸
const getImgWindowSize = (real_width, real_height) => {
  let width = real_width;
  let height = real_height;
  const other_height = 44 + 44; // 扣除顶部底部bar
  // 上下左右的留白
  const width_spare = 120;
  const height_spare = 200 + other_height;
  // 主窗口位置
  const homeWinBounds = mainIPC.getMainWindow().getBounds();

  // 多屏幕时判断是在哪面屏幕打开
  const cursor_point = screen.getCursorScreenPoint();
  const curr_screen = screen.getDisplayNearestPoint({
    x: cursor_point.x,
    y: cursor_point.y,
  });
  const screen_width = curr_screen.bounds.width;
  const screen_height = curr_screen.bounds.height;

  // 判断图片宽高是否超过屏幕宽高
  if (
    width > screen_width - width_spare ||
    height > screen_height - height_spare
  ) {
    const rate = height / width;
    if (width > screen_width - width_spare) {
      width = screen_width - width_spare;
      if (rate * (screen_width - width_spare) < screen_height - height_spare) {
        height = rate * (screen_width - width_spare);
      } else {
        height = screen_height - height_spare;
        width = (screen_height - height_spare) / rate;
      }
    } else if (height > screen_height - height_spare) {
      height = screen_height - height_spare;
      if ((screen_height - height_spare) / rate < screen_width - width_spare) {
        width = (screen_height - height_spare) / rate;
      } else {
        width = screen_width - width_spare;
        height = (screen_width - width_spare) * rate;
      }
    }
  }
  width = width > 300 ? width : 300;
  height = height > 400 ? height : 400;
  width = parseInt(width, 10);
  height = parseInt(height + other_height, 10);
  let x = Math.ceil((screen_width - width) / 2);
  let y = Math.ceil((screen_height - height) / 2);
  if (homeWinBounds?.width && homeWinBounds.height) {
    x = Math.ceil(homeWinBounds.x + (homeWinBounds.width - width) / 2);
    y = Math.ceil(homeWinBounds.y + (homeWinBounds.height - height) / 2);
  }
  return [width, height, x, y];
};

// 图片预览窗口-展示子窗口
const showBrowserWin_Img = async (props: {
  type: number;
  title: string;
  option?: any;
  params?: any;
}) => {
  const { option, params } = props;
  const [width, height, x, y] = getImgWindowSize(
    option.width ? option.width : 300,
    option.height ? option.height : 400
  );
  const res = await showBrowserWin({
    ...props,
    option: {
      ...option,
      width,
      height,
      x,
      y,
    },
    callback: (win: BrowserWindow) => {
      win.webContents.send('update-image', params.filepath);
    },
  });
  return res;
};

// 固定宽高的预览窗口-展示子窗口
const showBrowserWin_Other = async (props: {
  type: number;
  title: string;
  option?: any;
  params?: any;
}) => {
  const { option, params } = props;
  const [width, height, x, y] = getOtherWindowSize(option.width, option.height);
  const res = await showBrowserWin({
    ...props,
    option: {
      ...option,
      width,
      height,
      x,
      y,
    },
    callback: (win: BrowserWindow) => {
      win.webContents.send('update-image', params.filepath);
    },
  });
  return res;
};
export default {
  prepareWindow,
  showBrowserWin_Img,
  showBrowserWin_Other,
};

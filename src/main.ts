/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
/* eslint-disable max-lines */
import * as IpcMsgId from '@lightenSDK/constant/global';
import {
  app,
  BrowserWindow,
  Menu,
  protocol,
  Tray,
  nativeImage,
  ipcMain,
  dialog,
  globalShortcut,
  session,
} from 'electron';
import fs from 'fs';
import * as Path from 'path';
import { mainIPC } from '@htElectronSDK/main/ipc';
import LOG from '@htElectronSDK/main/log';
import { forceReload } from '@lightenSDK/main/menu';
import {
  setAsProtocolLaunchClient,
  handleProtocolLaunch4Win,
  handleProtocolLaunch,
} from '@lightenSDK/main/protocol';
import { initConfigPath } from '@lightenModule/configFile';
import { getLibSuffix } from '@utils/openim';
import path from 'node:path';
import OpenIMSDKMain from '@ht/openim-electron-client-sdk';
import { initMain } from './mainSDK';
import packInfo from '../package.json';
import logicWindow from './htElectronSDK/main/logicWindow';
import { preCapture, startScreenshot } from './utils/capture/capturetools';

declare const LINKFLOW_WEBPACK_ENTRY: string; // LINKFLOW_ 是窗口名字
declare const LINKFLOW_PRELOAD_WEBPACK_ENTRY: string; //

let mainWindow: Electron.BrowserWindow | null;
let popupWindow: Electron.BrowserWindow | null;

let tray;
let flashInterval = null;
let realQuit = false;
let needShowPopupWindow = false;
let trayLeaveTime = null;
let mouseLeavePromise = false;

if (
  process.env.NODE_ENV === 'development' ||
  process.env.SERVER === 'sit' ||
  process.env.SERVER === 'uat'
) {
  // 将HTTP协议注册为特权协议，用于获取麦克风权限
  protocol.registerSchemesAsPrivileged([
    {
      scheme: 'http',
      privileges: {
        standard: true,
        secure: true,
        bypassCSP: true,
        allowServiceWorkers: true,
        supportFetchAPI: true,
        corsEnabled: true,
        stream: true,
      },
    },
  ]);
}

setAsProtocolLaunchClient();

export const setRealQuit = () => {
  realQuit = true;
};

// 初始化注册快捷键
const initShortKeys = async () => {
  const defaultShortKeys = {
    screenShot: 'Shift+Alt+A',
  };
  try {
    // 截图快捷键
    globalShortcut.register(defaultShortKeys.screenShot, () => {
      startScreenshot(false);
    });
  } catch (err) {}
};

function createWindow() {
  initConfigPath();
  preCapture();
  initShortKeys();
  mainWindow = new BrowserWindow({
    frame: false,
    width: 1500,
    height: 980,
    minWidth: 960,
    minHeight: 600,
    // maxWidth: 1920,
    // maxHeight: 1080,
    // useContentSize: true,
    transparent: true,
    webPreferences: {
      nodeIntegration: true, // 是否集成 Nodejs,把之前预加载的js去了，发现也可以运行
      preload: LINKFLOW_PRELOAD_WEBPACK_ENTRY, // Path.join(__dirname, '../renderer/lighten/preload.js'),
    },
  });
  console.log('LINKFLOW_PRELOAD_WEBPACK_ENTRY', LINKFLOW_PRELOAD_WEBPACK_ENTRY);
  console.log('LINKFLOW_WEBPACK_ENTRY', LINKFLOW_WEBPACK_ENTRY);
  console.log('process.env.NODE_ENV', process.env.NODE_ENV);

  // if (process.env.NODE_ENV === 'production' && process.env.SERVER === 'prod') {
  //   mainWindow.loadURL(
  //     'https://lighten.lhzq.com:14443/ione-lighten-renderer/index.html'
  //   );
  // } else {
  mainWindow.loadURL(LINKFLOW_WEBPACK_ENTRY); // 固定的，不要乱修改
  // }

  // if (
  //   process.env.SERVER === 'uat' ||
  //   process.env.SERVER === 'sit' ||
  //   process.env.NODE_ENV === 'development'
  // ) {
  mainWindow.webContents.openDevTools({ mode: 'detach' });
  // }

  const libPath =
    process.env.NODE_ENV === 'development'
      ? path.join(
          __dirname,
          '../../node_modules/@ht/openim-electron-client-sdk/assets',
          getLibSuffix()
        )
      : path.join(
          process.resourcesPath,
          'openim-electron-client-sdk/assets',
          getLibSuffix()
        );
  console.log('libPath', libPath);
  console.log('NODE_ENV', process.env.NODE_ENV);
  LOG.info('libPath', libPath);

  const openIM = new OpenIMSDKMain(libPath, mainWindow.webContents);
  initMain(mainWindow); // htElectronSDK的初始化

  mainWindow.on('close', (e) => {
    if (!realQuit) {
      e.preventDefault();
      if (mainWindow) {
        mainWindow.hide();
      }
    } else {
      mainWindow = null;
    }
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  mainWindow.webContents.on(
    'did-fail-load',
    (
      _details,
      errorCode,
      errorDescription,
      validatedURL,
      isMainFrame,
      formattedPrice,
      frameRoutingId
    ) => {
      // 捕获index.html静态资源加载失败的情况
      mainIPC.send({
        id: IpcMsgId.LINKFLOW_CUSTOM_CATCH_UPLOAD,
        params: {
          name: 'did-fail-load',
          message: {
            errorCode,
            errorDescription,
            validatedURL,
            isMainFrame,
            formattedPrice,
            frameRoutingId,
            mainVersion: packInfo.version,
          },
        },
      });
      LOG.error(
        'did-fail-load',
        errorCode,
        errorDescription,
        validatedURL,
        isMainFrame,
        formattedPrice,
        frameRoutingId
      );
      // 清除缓存后重载简富
      forceReload();
      // mainWindow.webContents.session
      //   .clearStorageData({
      //     storages: ['cachestorage', 'filesystem', 'cookies'],
      //   })
      //   .then(() => {
      //     mainWindow.loadURL(`${LINKFLOW_WEBPACK_ENTRY}?retry=true`);
      //   })
      //   .catch((reason) => {
      //     LOG.info('did-fail-load.catch', reason);
      //     mainWindow.loadURL(`${LINKFLOW_WEBPACK_ENTRY}?retry=true`);
      //   });
    }
  );

  // 窗口预创建
  setTimeout(async () => {
    logicWindow.prepareWindow();
  }, 1);
}

app.on('before-quit', () => {
  if (process.platform === 'darwin') {
    realQuit = true;
  }
});

app.on('ready', () => {
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  if (require('electron-squirrel-startup')) {
    app.quit();
    return;
  }
  createWindow();
  createTray();
  mainWindow.on('show', () => {
    stopFlashing();
  });
  mainWindow.on('focus', () => {
    stopFlashing();
  });
  mainWindow.on('restore', () => {
    stopFlashing();
  });
  // Windows响应外部协议（首次启动）
  if (process.platform === 'win32') {
    handleProtocolLaunch4Win(process.argv);
  }
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow();
  } else {
    // Mac上点击dock图标，需要调用show把window展示出来
    mainWindow.show();
  }
});

function clearCookie() {
  session.defaultSession.cookies
    .get({})
    .then((cookies) => {
      cookies.forEach((cookie) => {
        let url = '';
        // get prefix, like https://www.
        url += cookie.secure ? 'https://' : 'http://';
        url += cookie.domain.startsWith('.') ? 'www' : '';
        // append domain and path
        url += cookie.domain;
        url += cookie.path;
        session.defaultSession.cookies.remove(url, cookie.name);
      });
    })
    .catch((error) => {
      console.log(error);
    });
}
// 系统托盘
function createTray() {
  if (process.platform !== 'darwin') {
    const iconPath = app.isPackaged
      ? Path.join(process.resourcesPath, 'logo.ico')
      : Path.join(app.getAppPath(), 'src/icon/logo.ico');
    tray = new Tray(iconPath);

    const contextMenu = Menu.buildFromTemplate([
      {
        label: '切换至DEV环境',
        click: () => {
          if (process.platform !== 'darwin') {
            clearCookie();
            mainWindow.loadURL('http://eipdev.htsc.com.cn/linkflow/chat');
          }
        },
      },
      {
        label: '切换至SIT环境',
        click: () => {
          if (process.platform !== 'darwin') {
            clearCookie();
            mainWindow.loadURL('http://eipsit.htsc.com.cn/linkflow/chat');
          }
        },
      },
      {
        label: '切换至PROD环境',
        click: () => {
          if (process.platform !== 'darwin') {
            clearCookie();

            mainWindow.loadURL('http://eipdev.htsc.com.cn/linkflow/chat');
          }
        },
      },
      {
        label: '退出',
        click: () => {
          if (process.platform !== 'darwin') {
            realQuit = true;
            app.quit();
          }
        },
      },
    ]);
    tray.setToolTip('LinkFlow');
    tray.setContextMenu(contextMenu);

    tray.on('click', () => {
      if (mainWindow) {
        mainWindow.restore();
        mainWindow.focus();
        mainWindow.show();
      }
    });
    tray.on('mouse-enter', () => {
      if (needShowPopupWindow) {
        if (trayLeaveTime) {
          clearTimeout(trayLeaveTime);
          trayLeaveTime = null;
        }
        popupWindow.show();
        mouseLeavePromise = false;
      }
    });
    tray.on('mouse-leave', () => {
      mouseLeave();
    });
    // 创建消息列托盘
    createPopupWindow();
  }
}

function mouseLeave() {
  if (!needShowPopupWindow) {
    return;
  }
  if (trayLeaveTime) {
    clearTimeout(trayLeaveTime);
    trayLeaveTime = null;
  }
  if (mouseLeavePromise) {
    return;
  }
  trayLeaveTime = setTimeout(() => {
    clearTimeout(trayLeaveTime);
    trayLeaveTime = null;
    popupWindow.hide();
  }, 300);
}

// 开始闪烁效果
function startFlashing() {
  if (flashInterval) {
    return;
  } // 防止重复启动

  let showAlt = false;
  flashInterval = setInterval(() => {
    const iconPath = app.isPackaged
      ? path.join(process.resourcesPath, showAlt ? '' : 'logo.ico')
      : Path.join(app.getAppPath(), showAlt ? '' : 'src/icon/logo.ico');
    const trayIcon = nativeImage.createFromPath(iconPath);
    tray.setImage(trayIcon);
    showAlt = !showAlt;
  }, 500); // 每500ms切换一次图标
}

// 停止闪烁效果
function stopFlashing() {
  if (flashInterval) {
    clearInterval(flashInterval);
    flashInterval = null;
    // 恢复默认图标
    const iconPath = app.isPackaged
      ? path.join(process.resourcesPath, 'logo.ico')
      : Path.join(app.getAppPath(), 'src/icon/logo.ico');
    const trayIcon = nativeImage.createFromPath(iconPath);
    tray.setImage(trayIcon);
  }
  needShowPopupWindow = false;
}

let dockBounceId;
// 窗口闪烁
function setflashFrame() {
  if (process.platform === 'win32') {
    mainWindow.flashFrame(true);
  } else if (process.platform === 'darwin') {
    dockBounceId = app.dock.bounce('informational');
  }
}

function stopflashFrame() {
  if (process.platform === 'win32') {
    mainWindow.flashFrame(false);
  } else if (process.platform === 'darwin') {
    app.dock.cancelBounce(dockBounceId);
  }
}

ipcMain.on('message-notification', (event, list, num) => {
  const flag = mainWindow && !mainWindow.isFocused();
  if (flag) {
    needShowPopupWindow = true;
    popupWindow?.webContents.send('get-message-notification-data', {
      list,
      num,
    });
    const popupHeight = 70 + 66 * (list.length || 0);
    const { x, y } = tray.getBounds();
    popupWindow.setBounds({
      x: x - 150,
      y: y - popupHeight,
      width: 300,
      height: popupHeight,
    });
    startFlashing();
    setflashFrame();
  }
});

ipcMain.on('notification-mouse-status', (event, status) => {
  mouseLeavePromise = true;
  if (status === 'enter') {
    clearTimeout(trayLeaveTime);
    trayLeaveTime = null;
  } else {
    popupWindow.hide();
  }
});
ipcMain.on('notification-cancel-flashing', (event, data) => {
  stopFlashing();
  stopflashFrame();
  popupWindow.hide();
});
ipcMain.on('notification-message-click', (event, data) => {
  popupWindow.hide();
  mainWindow.show();
  mainWindow?.webContents.send('notification-message-click-callback', data);
});
ipcMain.handle('open-file-dialog', async (event, type, filters) => {
  try {
    // eslint-disable-next-line no-inner-declarations
    function getMimeType(filePath: string) {
      const ext = filePath.split('.').pop().toLowerCase();
      const mimeTypes = {
        jpg: 'image/jpeg',
        jpeg: 'image/jpeg',
        png: 'image/png',
        gif: 'image/gif',
        webp: 'image/webp',
      };
      return mimeTypes[ext] || 'image/jpeg';
    }
    // 唤起文件选择对话框
    const result = await dialog.showOpenDialog({
      properties: type,
      filters,
    });
    const flag = result.canceled || result.filePaths.length === 0;
    if (!flag) {
      const filePath = result.filePaths[0];
      const stats = fs.statSync(filePath);
      const image = nativeImage.createFromPath(filePath);
      const dataUrl = image.toDataURL();
      const file = {
        previewUrl: dataUrl,
        path: filePath,
        name: filePath.split(/[\\/]/).pop(), // 提取文件名
        size: stats.size,
      };
      return {
        isSuccess: true,
        file,
      };
    } else {
      return {
        isSuccess: false,
      };
    }
  } catch (error) {
    console.error('处理文件时出错:', error);
    return {
      isSuccess: false,
    };
  }
});

const getPopupWindowHref = () => {
  if (process.env.SERVER === 'development') {
    return 'http://localhost:8000/linkflow/notification';
  } else if (process.env.SERVER === 'sit') {
    return 'http://eipdev.htsc.com.cn/linkflow/notification';
  } else if (process.env.SERVER === 'uat') {
    return 'http://eipsit.htsc.com.cn/linkflow/notification';
  } else if (process.env.NODE_ENV === 'prod') {
    return 'http://eip.htsc.com.cn/linkflow/notification';
  } else if (process.env.SERVER === 'lite') {
    return 'http://eiplite.htsc.com.cn/linkflow/notification';
  } else {
    return 'http://eipsit.htsc.com.cn/linkflow/notification';
  }
};

function createPopupWindow() {
  popupWindow = new BrowserWindow({
    width: 300,
    height: 400,
    show: false,
    frame: false,
    resizable: true,
    transparent: true,
    alwaysOnTop: true, // 指定窗口是否一直处于其他窗口之上
    skipTaskbar: true, // 是否在任务栏中显示窗口
    webPreferences: {
      enablePreferredSizeMode: true, // 高度根据内容自适应
      nodeIntegration: true, // 是否集成 Nodejs,把之前预加载的js去了，发现也可以运行
      preload: LINKFLOW_PRELOAD_WEBPACK_ENTRY, // Path.join(__dirname, '../renderer/lighten/preload.js'),
    },
  });
  const url = getPopupWindowHref();
  popupWindow.loadURL(url);
  // popupWindow.webContents.openDevTools({ mode: 'detach' });
  popupWindow.setIgnoreMouseEvents(false);
}

// 只允许开一个主窗口
const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', (_event, _commandLine, _workingDirectory) => {
    // 当运行第二个实例时,将会聚焦到mainWindow这个窗口
    if (mainWindow) {
      if (mainWindow.isMinimized()) {
        mainWindow.restore();
      }
      mainWindow.focus();
      mainWindow.show();
      // Windows响应外部协议（二次唤醒）
      if (process.platform === 'win32') {
        handleProtocolLaunch4Win(_commandLine);
      }
    }
  });
}

/**
 * macOS响应外部协议（首次启动和二次唤醒都走这里）
 */
app.on('open-url', (event, urlStr) => {
  if (app.isReady() && mainWindow) {
    if (mainWindow.isMinimized()) {
      mainWindow.restore();
    }
    if (!mainWindow.isFocused()) {
      mainWindow.focus();
    }
    if (!mainWindow.isVisible()) {
      mainWindow.show();
    }
  }
  handleProtocolLaunch(urlStr);
});

process.on('unhandledRejection', (reason) => {
  LOG.info(reason);
  mainIPC.send({
    id: IpcMsgId.LINKFLOW_CUSTOM_CATCH_UPLOAD,
    params: {
      name: 'unhandledRejection',
      message: {
        error: reason,
        from: 'node',
        type: 'unhandledRejection',
        mainVersion: packInfo.version,
      },
    },
  });
});

process.on('uncaughtException', (err) => {
  LOG.info('uncaughtException: err', err.stack || err.message);
  mainIPC.send({
    id: IpcMsgId.LINKFLOW_CUSTOM_CATCH_UPLOAD,
    params: {
      name: 'uncaughtException',
      message: {
        error: err,
        message: err.stack || err.message,
        from: 'node',
        type: 'uncaughtException',
        mainVersion: packInfo.version,
      },
    },
  });
});
